import HookFormItem from '@/components/hook-form/HookFormItem';
import { Form, Input } from '@/components/ui';
import { useForm } from 'react-hook-form';

const JobApplicationForm = () => {
  const form = useForm();
  return (
    <div className='mx-auto max-w-4xl bg-green-400 pt-20'>
      {/* <h3 className='text-gray-dark mb-6 text-2xl'>Application Form</h3> */}
      <Form {...form}>
        <form>
          <HookFormItem name='full_name' label='Full Name' className='w-full'>
            <Input placeholder='Enter your full name' className='w-full' />
          </HookFormItem>
        </form>
      </Form>
    </div>
  );
};
export default JobApplicationForm;
